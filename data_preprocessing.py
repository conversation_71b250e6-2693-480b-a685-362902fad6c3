import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.feature_selection import SelectKBest, f_regression
from datetime import datetime, timedelta
import torch
import logging

class DataPreprocessor:
    def __init__(self, scaler_type='standard', k_best_features=10):
        """
        初始化数据预处理器

        Args:
            scaler_type (str): 缩放器类型，'standard' 或 'minmax'
            k_best_features (int): 特征选择的特征数量
        """
        # 支持标准化和归一化
        if scaler_type == 'minmax':
            self.scaler = MinMaxScaler()
        else:
            self.scaler = StandardScaler()
        self.scaler_type = scaler_type
        self.k_best_features = k_best_features
        self.selected_features = None

        # 新数据集的列名映射（中文到英文）
        self.column_mapping = {
            'start_time': 'Timestamp',  # 修复：实际数据文件中的时间列名是start_time
            '降水量(mm)': 'Precipitation_mm',
            '平均气温(℃)': 'Average_Temperature_C',
            '最低气温(℃)': 'Min_Temperature_C',
            '最高气温(℃)': 'Max_Temperature_C',
            '总有功功率_总和(kW)': 'Charging_Load_kW'
        }

        # 更新数值特征列表（适配新数据集）
        self.numerical_features = [
            'Precipitation_mm',
            'Average_Temperature_C',
            'Min_Temperature_C',
            'Max_Temperature_C'
        ]
        
    def load_and_clean_data(self, file_path):
        """
        加载并清洗电动汽车充电数据

        Args:
            file_path (str): 数据文件路径

        Returns:
            pd.DataFrame: 清洗后的数据框
        """
        # 读取CSV文件，处理中文编码
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
        except UnicodeDecodeError:
            df = pd.read_csv(file_path, encoding='gbk')

        # 重命名列（中文到英文）
        df = df.rename(columns=self.column_mapping)

        # 转换时间列，处理新的时间格式
        if 'Timestamp' in df.columns:
            # 尝试多种时间格式
            try:
                df['Timestamp'] = pd.to_datetime(df['Timestamp'], format='%Y/%m/%d %H:%M')
            except ValueError:
                try:
                    df['Timestamp'] = pd.to_datetime(df['Timestamp'])
                except ValueError as e:
                    logging.error(f"时间格式转换失败: {e}")
                    raise

        # 确保数值列为浮点型
        for col in self.numerical_features + ['Charging_Load_kW']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # 基本数据质量检查
        logging.info(f"数据加载完成，共 {len(df)} 行，{len(df.columns)} 列")
        logging.info(f"时间范围: {df['Timestamp'].min()} 到 {df['Timestamp'].max()}")

        return df
    
    def fill_missing_values(self, df):
        """填充缺失值"""
        # 使用前向填充和后向填充
        df = df.ffill().bfill()
        
        # 对于连续缺失值，使用上周和下周相同时间的平均值填充
        for col in df.columns:
            if df[col].isnull().any():
                null_indices = df[col].isnull()
                for idx in null_indices[null_indices].index:
                    current_time = df.loc[idx, 'Timestamp']
                    prev_week_time = current_time - pd.Timedelta(days=7)
                    next_week_time = current_time + pd.Timedelta(days=7)
                    
                    prev_week_val = df.loc[df['Timestamp'] == prev_week_time, col].values
                    next_week_val = df.loc[df['Timestamp'] == next_week_time, col].values
                    
                    if len(prev_week_val) > 0 and len(next_week_val) > 0:
                        df.loc[idx, col] = (prev_week_val[0] + next_week_val[0]) / 2
        
        return df
    
    def remove_outliers(self, df, columns, z_thresh=3):
        """去除数值型特征的异常值（z-score法）"""
        for col in columns:
            if col in df.columns:
                # 检查列是否全为NaN，以避免std()为0的情况
                if df[col].std() == 0:
                    continue
                col_zscore = (df[col] - df[col].mean()) / df[col].std()
                # 异常值设为NaN，后续填充或者删除
                df.loc[np.abs(col_zscore) >= z_thresh, col] = np.nan
        return df
    
    def feature_correlation_analysis(self, df, target_col, threshold=0.05):
        """剔除与目标变量低相关的特征"""
        numeric_df = df.select_dtypes(include=[np.number])
        if target_col not in numeric_df.columns or len(numeric_df.columns) < 2:
            return df
        corr_matrix = numeric_df.corr().abs()
        if target_col not in corr_matrix.columns:
            return df
        corr = corr_matrix[target_col]
        selected = corr[corr > threshold].index.tolist()
        if target_col not in selected:
            selected.append(target_col)
        keep_cols = [col for col in df.columns if col in selected or col == 'Timestamp']
        return df[keep_cols]
    
    def create_24h_load_matrix(self, df):
        """
        将充电数据转换为24小时负荷矩阵

        Args:
            df (pd.DataFrame): 包含时间戳和充电负荷的数据框

        Returns:
            pd.DataFrame: 24小时负荷矩阵
        """
        # 确保Timestamp列是datetime类型
        df['Timestamp'] = pd.to_datetime(df['Timestamp'])

        # 创建一个时间索引的DataFrame，以小时为频率
        date_range = pd.date_range(
            start=df['Timestamp'].min().floor('h'),
            end=df['Timestamp'].max().ceil('h'),
            freq='h'
        )

        hourly_load = pd.DataFrame(index=date_range, columns=['load'])
        hourly_load['load'] = 0.0  # 明确指定为浮点型

        # 将每次充电分配到对应的小时时段
        # 假设Charging_Load_kW是瞬时负荷，这里简单地聚合到小时
        # 如果是累计负荷，需要不同的处理逻辑
        df_hourly = df.set_index('Timestamp').resample('h')['Charging_Load_kW'].sum().reindex(date_range, fill_value=0)
        hourly_load['load'] = df_hourly.values

        return hourly_load

    def create_weather_features(self, df):
        """
        创建气象相关特征

        Args:
            df (pd.DataFrame): 包含气象数据的数据框

        Returns:
            pd.DataFrame: 添加气象特征后的数据框
        """
        df = df.copy()

        # 温度相关特征
        df['Temperature_Range'] = df['Max_Temperature_C'] - df['Min_Temperature_C']  # 温差
        df['Temperature_Median'] = (df['Max_Temperature_C'] + df['Min_Temperature_C']) / 2  # 温度中位数
        df['Temperature_Deviation'] = df['Average_Temperature_C'] - df['Temperature_Median']  # 温度偏差

        # 气象交互特征
        df['Temp_Precipitation_Interaction'] = df['Average_Temperature_C'] * df['Precipitation_mm']
        df['Range_Precipitation_Interaction'] = df['Temperature_Range'] * df['Precipitation_mm']

        # 温度平方项（非线性特征）
        df['Average_Temperature_Squared'] = df['Average_Temperature_C'] ** 2

        # 降水量分类特征
        df['Is_Rainy'] = (df['Precipitation_mm'] > 0).astype(int)
        df['Rain_Category'] = pd.cut(df['Precipitation_mm'],
                                bins=[-0.1, 0, 5, 15, float('inf')],
                                labels=[0, 1, 2, 3])  # 无雨、小雨、中雨、大雨

        return df

    def create_enhanced_time_features(self, df):
        """
        创建增强的时间特征

        Args:
            df (pd.DataFrame): 包含时间戳的数据框

        Returns:
            pd.DataFrame: 添加时间特征后的数据框
        """
        df = df.copy()

        # 基础时间特征
        df['hour'] = df['Timestamp'].dt.hour
        df['day_of_week'] = df['Timestamp'].dt.dayofweek
        df['day_of_month'] = df['Timestamp'].dt.day
        df['day_of_year'] = df['Timestamp'].dt.dayofyear
        df['week_of_year'] = df['Timestamp'].dt.isocalendar().week.astype(int)
        df['month'] = df['Timestamp'].dt.month
        df['quarter'] = df['Timestamp'].dt.quarter

        # 周期性特征（使用三角函数编码）
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['day_of_week_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
        df['day_of_week_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)

        # 工作日/周末标识
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)

        # 时段分类
        df['time_period'] = pd.cut(df['hour'],
                                  bins=[-1, 6, 12, 18, 24],
                                  labels=[0, 1, 2, 3])  # 夜间、上午、下午、晚间

        # 峰谷时段标识（基于充电行为模式）
        df['is_peak_hour'] = ((df['hour'] >= 18) & (df['hour'] <= 23)).astype(int)
        df['is_valley_hour'] = ((df['hour'] >= 2) & (df['hour'] <= 6)).astype(int)

        return df

    def add_weather_lag_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        为气象数据添加滞后特征和滚动统计特征

        Args:
            df (pd.DataFrame): 包含气象数据的数据框

        Returns:
            pd.DataFrame: 添加气象滞后特征后的数据框
        """
        df = df.copy()

        # 确保按时间排序
        df = df.sort_values("Timestamp").reset_index(drop=True)

        # 气象数据滞后特征
        weather_cols = ['Precipitation_mm', 'Average_Temperature_C', 'Min_Temperature_C', 'Max_Temperature_C']

        for col in weather_cols:
            if col in df.columns:
                # 滞后特征（1小时、3小时、6小时、24小时前）
                df[f"{col}_lag_1"] = df[col].shift(1)
                df[f"{col}_lag_3"] = df[col].shift(3)
                df[f"{col}_lag_6"] = df[col].shift(6)
                df[f"{col}_lag_24"] = df[col].shift(24)

                # 滚动平均特征
                df[f"{col}_roll_mean_3"] = df[col].rolling(window=3, min_periods=1).mean().shift(1)
                df[f"{col}_roll_mean_6"] = df[col].rolling(window=6, min_periods=1).mean().shift(1)
                df[f"{col}_roll_mean_12"] = df[col].rolling(window=12, min_periods=1).mean().shift(1)
                df[f"{col}_roll_mean_24"] = df[col].rolling(window=24, min_periods=1).mean().shift(1)

                # 滚动标准差特征
                df[f"{col}_roll_std_6"] = df[col].rolling(window=6, min_periods=1).std().shift(1)
                df[f"{col}_roll_std_24"] = df[col].rolling(window=24, min_periods=1).std().shift(1)

                # 滚动最大值和最小值
                df[f"{col}_roll_max_6"] = df[col].rolling(window=6, min_periods=1).max().shift(1)
                df[f"{col}_roll_min_6"] = df[col].rolling(window=6, min_periods=1).min().shift(1)

                # 变化率特征
                df[f"{col}_change_1"] = df[col].diff().shift(1)
                df[f"{col}_change_3"] = df[col].diff(3).shift(1)

        return df
    
    def add_time_lag_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """为数据帧添加充电负荷的时间滞后与滚动统计特征。

        本方法只依赖历史信息，不会引入未来数据泄漏。

        Args:
            df: 原始或预处理中的 DataFrame，要求包含 ``Timestamp`` 与 ``Charging_Load_kW`` 列。

        Returns:
            添加新特征后的 DataFrame（浅拷贝）。
        """
        df = df.copy()

        # 确保按时间排序，避免 shift 错位
        df = df.sort_values("Timestamp").reset_index(drop=True)

        # --- 基础滞后特征（上一小时/上一日/上一周） --- #
        df["load_lag_1"] = df["Charging_Load_kW"].shift(1)
        df["load_lag_24"] = df["Charging_Load_kW"].shift(24)
        df["load_lag_168"] = df["Charging_Load_kW"].shift(168)  # 24*7

        # --- 滚动平均特征（历史窗口，不含当前行） --- #
        df["load_roll_mean_3"] = (
            df["Charging_Load_kW"].rolling(window=3, min_periods=1).mean().shift(1)
        )
        df["load_roll_mean_6"] = (
            df["Charging_Load_kW"].rolling(window=6, min_periods=1).mean().shift(1)
        )
        df["load_roll_mean_12"] = (
            df["Charging_Load_kW"].rolling(window=12, min_periods=1).mean().shift(1)
        )
        df["load_roll_mean_24"] = (
            df["Charging_Load_kW"].rolling(window=24, min_periods=1).mean().shift(1)
        )

        # --- 滚动统计特征（标准差、最大值、最小值） --- #
        df["load_roll_std_3"] = (
            df["Charging_Load_kW"].rolling(window=3, min_periods=1).std().shift(1)
        )
        df["load_roll_std_6"] = (
            df["Charging_Load_kW"].rolling(window=6, min_periods=1).std().shift(1)
        )
        df["load_roll_max_3"] = (
            df["Charging_Load_kW"].rolling(window=3, min_periods=1).max().shift(1)
        )
        df["load_roll_max_6"] = (
            df["Charging_Load_kW"].rolling(window=6, min_periods=1).max().shift(1)
        )

        # --- 差分特征 --- #
        df["load_diff_1"] = df["Charging_Load_kW"].diff().shift(1)
        df["load_diff_24"] = df["Charging_Load_kW"].diff(24).shift(1)

        return df
    
    def fit_features(self, df):
        """
        在提供的DataFrame的数值特征上拟合缩放器

        Args:
            df (pd.DataFrame): 用于拟合缩放器的数据框
        """
        # 应用完整的特征工程流程
        df = self.create_weather_features(df)
        df = self.create_enhanced_time_features(df)
        df = self.add_time_lag_features(df)
        df = self.add_weather_lag_features(df)

        # 动态识别所有数值列，排除时间戳和目标列
        features_to_scale = [col for col in df.select_dtypes(include=np.number).columns
                             if col not in ['Timestamp', 'target', 'Charging_Load_kW']]

        if not features_to_scale:
            logging.warning("没有可用于缩放的数值特征。")
            self.scaled_columns = []
            return

        temp_df = df[features_to_scale].copy()
        # 确保列为数值类型，处理潜在的非数值数据
        for col in temp_df.columns:
            temp_df[col] = pd.to_numeric(temp_df[col], errors='coerce')

        self.scaler.fit(temp_df.dropna())  # fit on non-NaN values
        self.scaled_columns = features_to_scale

        logging.info(f"特征缩放器已拟合，共 {len(self.scaled_columns)} 个特征")

    def transform_features(self, df):
        """Transform the numerical features of the provided DataFrame using the fitted scaler."""
        temp_df = df.copy() # Work on a copy to avoid modifying original df directly if not intended
        
        if not hasattr(self, 'scaled_columns') or not self.scaled_columns:
            logging.warning("Scaler 未拟合或没有要转换的特征。")
            return temp_df

        # 确保只转换 fit_features 时使用的列
        cols_to_transform = [col for col in self.scaled_columns if col in temp_df.columns]

        if not cols_to_transform:
            logging.warning("当前DataFrame中没有需要转换的特征。")
            return temp_df

        # 确保列为数值类型，处理潜在的非数值数据
        for col in cols_to_transform:
            temp_df[col] = pd.to_numeric(temp_df[col], errors='coerce')
            
        # 转换前填充NaN，避免MinMaxScaler/StandardScaler报错
        temp_df[cols_to_transform] = temp_df[cols_to_transform].fillna(temp_df[cols_to_transform].mean())

        temp_df[cols_to_transform] = self.scaler.transform(temp_df[cols_to_transform])
        return temp_df
    
    def prepare_features(self, df, sequence_length=24):
        """
        准备模型输入特征（针对新数据集优化）
        注意：该方法假定数值特征的scaler已经通过fit_features方法进行了拟合。

        Args:
            df (pd.DataFrame): 输入数据框
            sequence_length (int): 序列长度

        Returns:
            pd.DataFrame: 处理后的特征数据框
        """
        # 1. 创建气象特征
        df = self.create_weather_features(df)

        # 2. 创建增强的时间特征
        df = self.create_enhanced_time_features(df)

        # 3. 添加充电负荷的时间滞后特征
        df = self.add_time_lag_features(df)

        # 4. 添加气象数据的滞后特征
        df = self.add_weather_lag_features(df)

        # 5. 异常值处理 - 应用于当前数据帧中所有数值列
        numeric_cols_for_outliers = df.select_dtypes(include=np.number).columns.tolist()
        # 排除目标列和时间戳列，因为它们不应该被当作异常值处理的特征
        numeric_cols_for_outliers = [col for col in numeric_cols_for_outliers
                                    if col not in ['Timestamp', 'Charging_Load_kW', 'target']]
        processed_df = self.remove_outliers(df.copy(), numeric_cols_for_outliers)

        # 6. 归一化/标准化
        # 使用已经拟合好的scaler进行转换
        processed_df = self.transform_features(processed_df)

        # 7. 填充异常值处理后可能产生的NaN
        processed_df = processed_df.ffill().bfill()  # 再次填充，因为remove_outliers可能引入NaN

        # 8. 设置目标变量（优化DataFrame性能）
        processed_df = processed_df.copy()  # 避免DataFrame碎片化
        processed_df['target'] = processed_df['Charging_Load_kW']

        # 9. 特征选择
        # 确定所有候选特征列（排除时间戳、目标变量和原始充电负荷）
        candidate_feature_cols = [col for col in processed_df.select_dtypes(include=np.number).columns
                                 if col not in ['Timestamp', 'target', 'Charging_Load_kW']]

        logging.info(f"候选特征数量: {len(candidate_feature_cols)}")

        # 如果selected_features尚未确定（首次调用，通常是训练数据），执行特征选择
        if self.selected_features is None:
            if self.k_best_features is not None and self.k_best_features < len(candidate_feature_cols):
                logging.info(f"使用SelectKBest进行特征选择，k={self.k_best_features}")
                selector = SelectKBest(score_func=f_regression, k=self.k_best_features)
                X = processed_df[candidate_feature_cols]
                y = processed_df['target']

                valid_indices = y.dropna().index
                X = X.loc[valid_indices]
                y = y.loc[valid_indices]

                if not X.empty and not y.empty:  # 确保数据不为空
                    selector.fit(X, y)  # Fit the selector
                    self.selected_features = [candidate_feature_cols[i] for i in selector.get_support(indices=True)]
                    logging.info(f"SelectKBest选择了 {len(self.selected_features)} 个特征")
                else:
                    logging.warning("用于特征选择的数据为空，回退到所有候选特征。")
                    self.selected_features = candidate_feature_cols  # Fallback if data is empty after dropna
            else:  # 使用相关性分析，如果k_best_features未指定或过大
                logging.info("使用相关性分析进行特征选择")
                temp_df_for_corr = processed_df.copy()
                # 确保目标列存在用于相关性分析
                if 'target' not in temp_df_for_corr.columns:
                     temp_df_for_corr['target'] = temp_df_for_corr['Charging_Load_kW']

                # 对候选特征列和目标进行相关性分析
                corr_df = temp_df_for_corr[candidate_feature_cols + ['target']]

                # 处理corr_df过小或全为NaN的情况
                if not corr_df.empty and 'target' in corr_df.columns and len(corr_df.columns) > 1:
                    processed_df_after_corr = self.feature_correlation_analysis(corr_df, 'target', threshold=0.05)
                    self.selected_features = [col for col in processed_df_after_corr.columns
                                            if col not in ['target', 'Timestamp']]
                    logging.info(f"相关性分析选择了 {len(self.selected_features)} 个特征")
                else:
                    logging.warning("用于相关性分析的数据不足或缺失，回退到所有候选特征。")
                    self.selected_features = candidate_feature_cols  # Fallback
        
        # Filter processed_df to keep only selected features, target, and Timestamp
        # Ensure all selected features are present in the current df
        final_feature_cols = [col for col in self.selected_features if col in processed_df.columns]
        
        # NEW: Fallback if final_feature_cols is empty after selection
        if not final_feature_cols:
            logging.warning("特征选择后没有选定任何特征。回退到所有数值特征。")
            final_feature_cols = [col for col in processed_df.select_dtypes(include=np.number).columns if col not in ['Timestamp', 'target', 'Charging_Load_kW']]
            if not final_feature_cols:
                raise ValueError("无法确定任何有效的输入特征。请检查数据和特征工程逻辑。")

        processed_df = processed_df[final_feature_cols + ['target', 'Timestamp']]
        
        return processed_df
    
    def prepare_sequences(self, df, sequence_length):
        """准备序列数据"""
        sequences = []
        targets = []
        
        # 确保数据按时间排序
        df = df.sort_values('Timestamp')
        
        # 动态获取特征列
        # 这里的selected_features应该已经在prepare_features中设置好
        feature_columns = self.selected_features
        
        # Fallback if selected_features is unexpectedly None or empty after prepare_features
        if not feature_columns:
            logging.warning("prepare_sequences中selected_features为空，尝试从df中动态获取数值特征。")
            feature_columns = [col for col in df.select_dtypes(include=np.number).columns if col not in ['Timestamp', 'target', 'Charging_Load_kW']]
            if not feature_columns:
                raise ValueError("无法确定任何有效的输入特征用于序列准备。请检查数据和特征工程逻辑。")

        # 确保feature_columns中的所有列都在df中
        feature_columns = [col for col in feature_columns if col in df.columns]

        # 创建序列
        for i in range(len(df) - sequence_length):
            sequence = df[feature_columns].iloc[i:(i + sequence_length)].values
            target = df['target'].iloc[i + sequence_length - 1]
            
            sequences.append(sequence)
            targets.append(target)
        
        # 转换为张量
        # 检查sequences和targets是否为空
        if not sequences or not targets:
            raise ValueError("No sequences or targets generated. Check data length and sequence_length.")

        X = torch.FloatTensor(np.array(sequences))
        y = torch.FloatTensor(np.array(targets)).reshape(-1, 1)
        
        return X, y
    
    def split_data(self, X, y):
        """按7:1:2的比例划分数据集"""
        train_size = int(0.7 * len(X))
        val_size = int(0.1 * len(X))
        
        X_train = X[:train_size]
        y_train = y[:train_size]
        
        X_val = X[train_size:train_size+val_size]
        y_val = y[train_size:train_size+val_size]
        
        X_test = X[train_size+val_size:]
        y_test = y[train_size+val_size:]
        
        return (X_train, y_train), (X_val, y_val), (X_test, y_test) 