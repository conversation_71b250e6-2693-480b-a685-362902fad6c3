import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import logging
import json
import os
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')
from skopt import gp_minimize
from skopt.space import Real, Integer, Categorical
from skopt.utils import use_named_args
import pickle

class AdvancedBayesianOptimizer:
    """
    增强的贝叶斯优化器，提供更稳健和准确的超参数优化
    
    主要改进：
    1. 扩展搜索空间（18个参数）
    2. 多目标优化（损失+复杂度+稳定性）
    3. 不确定性量化
    4. 历史信息利用
    5. 自适应优化策略
    """
    
    def __init__(self, 
                 output_dir: str,
                 n_calls: int = 150,
                 n_initial_points: int = 20,
                 acquisition_func: str = 'EI',
                 enable_uncertainty_quantification: bool = True,
                 enable_multi_objective: bool = True,
                 enable_warm_start: bool = True,
                 random_state: int = 42):
        """
        初始化增强贝叶斯优化器
        
        Args:
            output_dir: 输出目录
            n_calls: 总优化调用次数
            n_initial_points: 初始随机采样点数
            acquisition_func: 获取函数类型 ('EI', 'LCB', 'PI')
            enable_uncertainty_quantification: 是否启用不确定性量化
            enable_multi_objective: 是否启用多目标优化
            enable_warm_start: 是否启用温启动
            random_state: 随机种子
        """
        self.output_dir = output_dir
        self.n_calls = n_calls
        self.n_initial_points = n_initial_points
        self.acquisition_func = acquisition_func
        self.enable_uncertainty_quantification = enable_uncertainty_quantification
        self.enable_multi_objective = enable_multi_objective
        self.enable_warm_start = enable_warm_start
        self.random_state = random_state
        
        # 优化历史
        self.optimization_history = []
        self.best_params_history = []
        self.convergence_data = []
        
        # 创建扩展的搜索空间
        self.search_space = self._create_extended_search_space()
        
        # 初始化高斯过程
        self.gp_model = None
        
        logging.info("增强贝叶斯优化器初始化完成")
        
    def _create_extended_search_space(self) -> List:
        """创建扩展的搜索空间"""
        space = [
            # 模型架构参数
            Integer(1, 8, name='num_layers'),
            Integer(64, 512, name='hidden_size'),
            Integer(2, 12, name='kernel_size'),
            Integer(1, 3, name='gru_layers'),
            Categorical([True, False], name='bidirectional_gru'),
            
            # 训练参数
            Real(0.1, 0.6, name='dropout'),
            Real(1e-4, 1e-2, 'log-uniform', name='learning_rate'),
            Real(1e-6, 1e-3, 'log-uniform', name='weight_decay'),
            Integer(32, 128, name='batch_size'),
            
            # 损失函数和优化器
            Categorical([0, 1, 2], name='loss_type'),  # MSE, L1, Huber
            Categorical([0, 1, 2], name='optimizer_type'),  # Adam, AdamW, RAdam
            
            # 数据预处理参数
            Integer(24, 72, name='sequence_length'),  # 增加序列长度范围
            Integer(15, 35, name='k_best_features'),  # 增加特征数量范围
            Real(0.3, 1.5, name='data_augmentation_strength'),
            
            # 训练策略参数
            Integer(5, 25, name='early_stopping_patience'),
            Real(0.3, 0.8, name='lr_scheduler_factor'),
            Integer(3, 15, name='lr_scheduler_patience'),
            
            # 正则化参数
            Real(0.0, 0.3, name='l2_regularization')
        ]
        
        logging.info(f"创建了包含 {len(space)} 个参数的扩展搜索空间")
        return space
    
    def _robust_objective_function(self, params: List, 
                                 train_loader: DataLoader, 
                                 val_loader: DataLoader,
                                 create_model_func,
                                 device: str) -> float:
        """
        稳健的目标函数，支持多目标优化和不确定性量化
        
        Args:
            params: 参数列表
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            create_model_func: 模型创建函数
            device: 设备
            
        Returns:
            综合评分（越小越好）
        """
        try:
            # 解析参数
            param_dict = self._parse_parameters(params)
            
            # 多次运行取平均，提高稳健性
            n_runs = 3 if self.enable_uncertainty_quantification else 1
            scores = []
            complexities = []
            stabilities = []
            
            for run in range(n_runs):
                # 设置不同的随机种子
                torch.manual_seed(self.random_state + run)
                np.random.seed(self.random_state + run)
                
                score, complexity, stability = self._single_run_evaluation(
                    param_dict, train_loader, val_loader, create_model_func, device
                )
                
                scores.append(score)
                complexities.append(complexity)
                stabilities.append(stability)
            
            # 计算平均值和不确定性
            mean_score = np.mean(scores)
            mean_complexity = np.mean(complexities)
            mean_stability = np.mean(stabilities)
            
            if self.enable_uncertainty_quantification and n_runs > 1:
                score_std = np.std(scores)
                uncertainty_penalty = 0.1 * score_std  # 惩罚不稳定的配置
            else:
                uncertainty_penalty = 0.0
            
            # 多目标优化 - 重点关注R²指标
            if self.enable_multi_objective:
                # 综合评分 = 主要损失(包含R²权重) + 轻微复杂度惩罚 + 稳定性惩罚 + 不确定性惩罚
                # 由于主要损失已经包含了R²的重罚，这里减少其他因素的权重
                final_score = (mean_score +
                             0.05 * mean_complexity +  # 降低复杂度权重
                             0.03 * (1 - mean_stability) +  # 降低稳定性权重
                             uncertainty_penalty)
            else:
                final_score = mean_score + uncertainty_penalty
            
            # 记录优化历史
            self.optimization_history.append({
                'params': param_dict,
                'score': mean_score,
                'complexity': mean_complexity,
                'stability': mean_stability,
                'uncertainty': score_std if self.enable_uncertainty_quantification and n_runs > 1 else 0.0,
                'final_score': final_score,
                'timestamp': datetime.now().isoformat()
            })
            
            logging.info(f"参数评估完成 - 损失: {mean_score:.6f}, 复杂度: {mean_complexity:.4f}, "
                        f"稳定性: {mean_stability:.4f}, 最终评分: {final_score:.6f}")
            
            return final_score
            
        except Exception as e:
            logging.error(f"目标函数评估失败: {e}")
            # 返回一个合理的惩罚值，不要太大以免影响优化器的数值稳定性
            return 2.0
    
    def _parse_parameters(self, params: List) -> Dict:
        """解析参数列表为字典，并进行类型转换"""
        param_names = [dim.name for dim in self.search_space]
        param_dict = dict(zip(param_names, params))

        # 对整数类型参数进行类型转换，确保是Python原生int类型
        integer_params = [
            'num_layers', 'hidden_size', 'kernel_size', 'gru_layers',
            'batch_size', 'sequence_length', 'k_best_features',
            'early_stopping_patience', 'lr_scheduler_patience'
        ]

        for param_name in integer_params:
            if param_name in param_dict:
                param_dict[param_name] = int(param_dict[param_name])

        return param_dict
    
    def _single_run_evaluation(self, param_dict: Dict,
                             train_loader: DataLoader,
                             val_loader: DataLoader,
                             create_model_func,
                             device: str) -> Tuple[float, float, float]:
        """
        单次运行评估 - 直接优化MAE、MSE和R²指标

        Returns:
            (综合评分, model_complexity, training_stability)
        """
        # 获取实际输入维度
        try:
            X_sample, _ = next(iter(train_loader))
            actual_input_size = X_sample.shape[2]
        except StopIteration:
            logging.error("训练数据加载器为空")
            return 1.0, 1.0, 0.0

        # 创建模型配置
        config = {
            'input_size': actual_input_size,
            'hidden_size': param_dict['hidden_size'],
            'num_layers': param_dict['num_layers'],
            'output_size': 1,
            'kernel_size': param_dict['kernel_size'],
            'dropout': param_dict['dropout'],
            'gru_layers': param_dict['gru_layers'],
            'l2_reg': param_dict.get('l2_regularization', 1e-4)
        }

        # 创建模型
        model = create_model_func(config).to(device)

        # 计算模型复杂度
        model_complexity = self._calculate_model_complexity(model)

        # 选择优化器
        optimizer_classes = {
            0: optim.Adam,
            1: optim.AdamW,
            2: optim.Adam  # 如果RAdam不可用，回退到Adam
        }

        try:
            if param_dict['optimizer_type'] == 2:
                # 尝试使用RAdam，如果不可用则回退到Adam
                try:
                    from torch_optimizer import RAdam
                    optimizer = RAdam(model.parameters(),
                                    lr=param_dict['learning_rate'],
                                    weight_decay=param_dict['weight_decay'])
                except ImportError:
                    optimizer = optim.Adam(model.parameters(),
                                         lr=param_dict['learning_rate'],
                                         weight_decay=param_dict['weight_decay'])
            else:
                optimizer_class = optimizer_classes[param_dict['optimizer_type']]
                optimizer = optimizer_class(model.parameters(),
                                          lr=param_dict['learning_rate'],
                                          weight_decay=param_dict['weight_decay'])
        except Exception:
            # 如果出现任何问题，回退到Adam
            optimizer = optim.Adam(model.parameters(),
                                 lr=param_dict['learning_rate'],
                                 weight_decay=param_dict['weight_decay'])

        # 训练模型（增加到10个epoch以提高评估稳定性）
        training_losses = []
        val_losses = []
        val_maes = []
        val_mses = []
        val_r2s = []

        for epoch in range(10):  # 增加训练轮数
            # 训练阶段
            model.train()
            epoch_train_loss = 0.0

            for X_batch, y_batch in train_loader:
                X_batch = X_batch.to(device, non_blocking=True)
                y_batch = y_batch.to(device, non_blocking=True)

                optimizer.zero_grad()
                pred = model(X_batch)

                # 使用加权损失函数，重点关注小值预测精度
                mse_loss = nn.MSELoss()(pred, y_batch)
                mae_loss = nn.L1Loss()(pred, y_batch)

                # 计算相对误差损失（对小值更敏感）
                epsilon = 1e-3
                relative_error = torch.mean(torch.abs(pred - y_batch) / (torch.abs(y_batch) + epsilon))

                # 计算Huber损失（对异常值更鲁棒）
                huber_loss = nn.SmoothL1Loss()(pred, y_batch)

                # 组合损失：MSE + MAE + 相对误差 + Huber
                loss = 0.3 * mse_loss + 0.3 * mae_loss + 0.2 * relative_error + 0.2 * huber_loss

                loss.backward()

                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

                optimizer.step()
                epoch_train_loss += loss.item()

            avg_train_loss = epoch_train_loss / len(train_loader)
            training_losses.append(avg_train_loss)

            # 验证阶段 - 计算MAE、MSE和R²
            model.eval()
            all_preds = []
            all_targets = []
            epoch_val_loss = 0.0

            with torch.no_grad():
                for X_val, y_val in val_loader:
                    X_val = X_val.to(device, non_blocking=True)
                    y_val = y_val.to(device, non_blocking=True)
                    pred_val = model(X_val)

                    # 计算验证损失（与训练损失保持一致）
                    mse_loss = nn.MSELoss()(pred_val, y_val)
                    mae_loss = nn.L1Loss()(pred_val, y_val)

                    epsilon = 1e-3
                    relative_error = torch.mean(torch.abs(pred_val - y_val) / (torch.abs(y_val) + epsilon))
                    huber_loss = nn.SmoothL1Loss()(pred_val, y_val)

                    val_loss = 0.3 * mse_loss + 0.3 * mae_loss + 0.2 * relative_error + 0.2 * huber_loss

                    epoch_val_loss += val_loss.item()

                    # 收集预测值和真实值用于计算R²
                    all_preds.extend(pred_val.cpu().numpy().flatten())
                    all_targets.extend(y_val.cpu().numpy().flatten())

            avg_val_loss = epoch_val_loss / len(val_loader)
            val_losses.append(avg_val_loss)

            # 计算验证集的MAE、MSE和R²
            all_preds = np.array(all_preds)
            all_targets = np.array(all_targets)

            val_mse = np.mean((all_preds - all_targets) ** 2)
            val_mae = np.mean(np.abs(all_preds - all_targets))

            # 计算R²
            ss_tot = np.sum((all_targets - np.mean(all_targets)) ** 2)
            ss_res = np.sum((all_targets - all_preds) ** 2)
            val_r2 = 1 - (ss_res / (ss_tot + 1e-8))  # 添加小值避免除零

            val_mses.append(val_mse)
            val_maes.append(val_mae)
            val_r2s.append(val_r2)

        # 计算训练稳定性
        training_stability = self._calculate_training_stability(training_losses, val_losses)

        # 使用最后几个epoch的平均值作为最终评估指标
        final_mse = np.mean(val_mses[-3:])  # 最后3个epoch的平均
        final_mae = np.mean(val_maes[-3:])
        final_r2 = np.mean(val_r2s[-3:])

        # 综合评分：重点优化R²，同时考虑MAE和MSE
        # 目标是最大化R²（接近0.995+），最小化MAE和MSE
        r2_penalty = max(0, 0.995 - final_r2) * 15  # 提高R²目标到0.995，增加惩罚权重

        # 归一化MSE和MAE（根据实际数据调整范围）
        normalized_mse = min(final_mse / 2.0, 1.0)  # 调整MSE合理范围
        normalized_mae = min(final_mae / 0.5, 1.0)  # 调整MAE合理范围

        # 增加MAPE惩罚
        mape = np.mean(np.abs((all_targets - all_preds) / (np.abs(all_targets) + 1e-3))) * 100
        mape_penalty = max(0, mape - 2.0) * 0.1  # MAPE超过2%时惩罚

        # 综合评分：进一步提高R²权重
        final_score = (0.6 * r2_penalty +
                      0.15 * normalized_mse +
                      0.15 * normalized_mae +
                      0.1 * mape_penalty)

        return final_score, model_complexity, training_stability
    
    def _calculate_model_complexity(self, model: nn.Module) -> float:
        """计算模型复杂度（参数数量的归一化值）"""
        total_params = sum(p.numel() for p in model.parameters())
        # 归一化到0-1范围，假设最大参数数量为1M
        normalized_complexity = min(total_params / 1e6, 1.0)
        return normalized_complexity
    
    def _calculate_training_stability(self, train_losses: List[float], 
                                    val_losses: List[float]) -> float:
        """
        计算训练稳定性
        
        Returns:
            稳定性分数（0-1，越高越稳定）
        """
        if len(train_losses) < 3 or len(val_losses) < 3:
            return 0.5  # 默认中等稳定性
        
        # 计算损失的变化趋势
        train_trend = np.polyfit(range(len(train_losses)), train_losses, 1)[0]
        val_trend = np.polyfit(range(len(val_losses)), val_losses, 1)[0]
        
        # 计算损失的方差
        train_var = np.var(train_losses)
        val_var = np.var(val_losses)
        
        # 稳定性评分：下降趋势好 + 方差小 = 稳定性高
        trend_score = max(0, -train_trend) + max(0, -val_trend)  # 负趋势是好的
        variance_score = 1.0 / (1.0 + train_var + val_var)  # 方差小是好的
        
        stability = (trend_score + variance_score) / 2
        return min(stability, 1.0)

    def optimize(self, train_loader: DataLoader, val_loader: DataLoader,
                create_model_func, device: str) -> Dict:
        """
        执行增强的贝叶斯优化

        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            create_model_func: 模型创建函数
            device: 设备

        Returns:
            最优参数字典
        """
        logging.info("开始增强贝叶斯优化...")

        # 尝试加载历史优化结果（温启动）
        history_file = os.path.join(self.output_dir, 'optimization_history.pkl')
        if self.enable_warm_start and os.path.exists(history_file):
            try:
                with open(history_file, 'rb') as f:
                    saved_history = pickle.load(f)
                loaded_history = saved_history.get('history', [])

                # 验证历史数据的结构一致性
                param_names = [dim.name for dim in self.search_space]
                valid_history = []

                for record in loaded_history:
                    if isinstance(record, dict) and 'score' in record and 'params' in record:
                        # 检查参数是否完整
                        if all(name in record['params'] for name in param_names):
                            valid_history.append(record)
                        else:
                            logging.debug(f"跳过不完整的历史记录: 缺少参数 {set(param_names) - set(record['params'].keys())}")
                    else:
                        logging.debug(f"跳过格式错误的历史记录: {type(record)}")

                self.optimization_history = valid_history
                logging.info(f"成功加载优化数据，历史记录: {len(self.optimization_history)} 条")

                if len(loaded_history) != len(valid_history):
                    logging.warning(f"过滤了 {len(loaded_history) - len(valid_history)} 条无效的历史记录")

            except Exception as e:
                logging.warning(f"加载历史记录失败: {e}")
                self.optimization_history = []

        # 定义目标函数
        @use_named_args(self.search_space)
        def objective(**params):
            param_list = [params[dim.name] for dim in self.search_space]
            return self._robust_objective_function(
                param_list, train_loader, val_loader, create_model_func, device
            )

        # 创建高斯过程内核（如果需要自定义内核）
        # kernel = Matern(length_scale=1.0, nu=2.5) + WhiteKernel(noise_level=1e-5)

        # 执行贝叶斯优化
        with tqdm(total=self.n_calls, desc="贝叶斯优化进度") as pbar:
            def callback(res):
                pbar.update(1)
                # 记录收敛数据
                current_score = res.func_vals[-1] if len(res.func_vals) > 0 else res.fun
                self.convergence_data.append({
                    'iteration': len(self.convergence_data) + 1,
                    'best_score': float(res.fun),
                    'current_score': float(current_score)
                })

                # 保存当前最佳参数
                if len(self.convergence_data) == 1 or float(res.fun) < min(cd['best_score'] for cd in self.convergence_data[:-1]):
                    best_params = dict(zip([dim.name for dim in self.search_space], res.x))
                    self.best_params_history.append({
                        'iteration': len(self.convergence_data),
                        'params': best_params,
                        'score': float(res.fun)
                    })

            # 执行优化
            result = gp_minimize(
                func=objective,
                dimensions=self.search_space,
                n_calls=self.n_calls,
                n_initial_points=self.n_initial_points,
                acq_func=self.acquisition_func,  # 使用正确的参数名
                random_state=self.random_state,
                callback=callback,
                n_jobs=1,  # 使用单进程避免并发问题
                verbose=False,
                noise=1e-10  # 添加小量噪声提高数值稳定性
            )

        # 解析最优参数
        best_params = dict(zip([dim.name for dim in self.search_space], result.x))

        # 映射分类参数
        loss_type_names = {0: 'MSELoss', 1: 'L1Loss', 2: 'SmoothL1Loss'}
        optimizer_type_names = {0: 'Adam', 1: 'AdamW', 2: 'RAdam'}

        best_params['loss_type'] = loss_type_names[best_params['loss_type']]
        best_params['optimizer_type'] = optimizer_type_names[best_params['optimizer_type']]

        # 保存优化结果
        self._save_optimization_results(best_params, result.fun)

        # 生成优化报告
        self._generate_optimization_report(best_params, result)

        logging.info(f"贝叶斯优化完成，最佳评分: {result.fun:.6f}")
        return best_params

    def _save_optimization_results(self, best_params: Dict, best_score: float):
        """保存优化结果"""
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 保存最优参数 (转换numpy类型为Python原生类型)
        serializable_params = {}
        for key, value in best_params.items():
            if hasattr(value, 'item'):  # numpy scalar
                serializable_params[key] = value.item()
            elif isinstance(value, (list, tuple)):
                serializable_params[key] = [v.item() if hasattr(v, 'item') else v for v in value]
            else:
                serializable_params[key] = value

        params_file = os.path.join(self.output_dir, 'best_params_advanced.json')
        with open(params_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_params, f, indent=4, ensure_ascii=False)

        # 保存优化历史
        history_file = os.path.join(self.output_dir, 'optimization_history.pkl')
        history_data = {
            'history': self.optimization_history,
            'convergence': self.convergence_data,
            'best_params_history': self.best_params_history,
            'final_best_score': best_score
        }
        with open(history_file, 'wb') as f:
            pickle.dump(history_data, f)

        logging.info(f"优化结果已保存到 {params_file}")

    def _generate_optimization_report(self, best_params: Dict, result):
        """生成详细的优化报告"""
        try:
            # 创建优化过程可视化
            self._plot_optimization_progress()
        except Exception as e:
            logging.warning(f"优化过程可视化生成失败: {e}")

        try:
            # 创建参数重要性分析
            self._plot_parameter_importance()
        except Exception as e:
            logging.warning(f"参数重要性分析生成失败: {e}")

        try:
            # 创建收敛性分析
            self._plot_convergence_analysis()
        except Exception as e:
            logging.warning(f"收敛性分析生成失败: {e}")

        # 生成文本报告
        report_file = os.path.join(self.output_dir, 'optimization_report.txt')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=== 增强贝叶斯优化报告 ===\n\n")
            f.write(f"优化完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总优化调用次数: {self.n_calls}\n")
            f.write(f"最佳评分: {result.fun:.6f}\n\n")

            f.write("最优参数:\n")
            for key, value in best_params.items():
                f.write(f"  {key}: {value}\n")

            f.write(f"\n优化历史记录数: {len(self.optimization_history)}\n")

            if self.optimization_history:
                scores = [h['score'] for h in self.optimization_history]
                f.write(f"平均评分: {np.mean(scores):.6f}\n")
                f.write(f"评分标准差: {np.std(scores):.6f}\n")
                f.write(f"最佳评分: {min(scores):.6f}\n")
                f.write(f"最差评分: {max(scores):.6f}\n")

        logging.info(f"优化报告已生成: {report_file}")

    def _plot_optimization_progress(self):
        """绘制优化过程图"""
        if not self.optimization_history:
            return

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

        # 评分历史
        scores = [h['score'] for h in self.optimization_history]
        iterations = range(1, len(scores) + 1)

        ax1.plot(iterations, scores, 'b-', alpha=0.7, label='评分')
        ax1.plot(iterations, np.minimum.accumulate(scores), 'r-', linewidth=2, label='最佳评分')
        ax1.set_title('优化过程 - 评分变化')
        ax1.set_xlabel('迭代次数')
        ax1.set_ylabel('评分')
        ax1.legend()
        ax1.grid(True)

        # 复杂度变化（如果数据可用）
        if all('complexity' in h for h in self.optimization_history):
            complexities = [h['complexity'] for h in self.optimization_history]
            ax2.plot(iterations, complexities, 'g-', alpha=0.7)
            ax2.set_title('模型复杂度变化')
            ax2.set_xlabel('迭代次数')
            ax2.set_ylabel('复杂度')
            ax2.grid(True)
        else:
            ax2.text(0.5, 0.5, '复杂度数据不可用', ha='center', va='center', transform=ax2.transAxes)
            ax2.set_title('模型复杂度变化')

        # 稳定性变化（如果数据可用）
        if all('stability' in h for h in self.optimization_history):
            stabilities = [h['stability'] for h in self.optimization_history]
            ax3.plot(iterations, stabilities, 'm-', alpha=0.7)
            ax3.set_title('训练稳定性变化')
            ax3.set_xlabel('迭代次数')
            ax3.set_ylabel('稳定性')
            ax3.grid(True)
        else:
            ax3.text(0.5, 0.5, '稳定性数据不可用', ha='center', va='center', transform=ax3.transAxes)
            ax3.set_title('训练稳定性变化')

        # 不确定性变化（如果启用且数据可用）
        if self.enable_uncertainty_quantification and all('uncertainty' in h for h in self.optimization_history):
            uncertainties = [h['uncertainty'] for h in self.optimization_history]
            ax4.plot(iterations, uncertainties, 'c-', alpha=0.7)
            ax4.set_title('预测不确定性变化')
            ax4.set_xlabel('迭代次数')
            ax4.set_ylabel('不确定性')
            ax4.grid(True)
        else:
            if self.enable_uncertainty_quantification:
                ax4.text(0.5, 0.5, '不确定性数据不可用', ha='center', va='center', transform=ax4.transAxes)
            else:
                ax4.text(0.5, 0.5, '不确定性量化未启用', ha='center', va='center', transform=ax4.transAxes)
            ax4.set_title('预测不确定性变化')

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'optimization_progress.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_parameter_importance(self):
        """绘制参数重要性分析"""
        if len(self.optimization_history) < 10:
            return

        # 计算参数与评分的相关性
        param_names = [dim.name for dim in self.search_space]
        param_values = {name: [] for name in param_names}
        scores = []

        # 确保所有历史记录都有完整的参数
        valid_history = []
        for history in self.optimization_history:
            if 'score' in history and 'params' in history:
                # 检查参数是否完整
                if all(name in history['params'] for name in param_names):
                    valid_history.append(history)

        if len(valid_history) < 10:
            logging.warning(f"有效历史记录不足 ({len(valid_history)} < 10)，跳过参数重要性分析")
            return

        # 使用有效的历史记录
        for history in valid_history:
            scores.append(history['score'])
            for name in param_names:
                param_values[name].append(history['params'][name])

        # 计算相关系数
        correlations = {}
        for name in param_names:
            try:
                values = param_values[name]
                if len(values) != len(scores):
                    logging.warning(f"参数 {name} 的值数量 ({len(values)}) 与评分数量 ({len(scores)}) 不匹配")
                    correlations[name] = 0
                    continue

                if isinstance(values[0], (int, float)) and len(set(values)) > 1:
                    # 确保数组长度一致且有变化
                    corr_matrix = np.corrcoef(values, scores)
                    if corr_matrix.shape == (2, 2):
                        corr = corr_matrix[0, 1]
                        correlations[name] = abs(corr) if not np.isnan(corr) else 0
                    else:
                        correlations[name] = 0
                else:
                    correlations[name] = 0  # 分类变量或无变化的变量
            except Exception as e:
                logging.warning(f"计算参数 {name} 的相关性失败: {e}")
                correlations[name] = 0

        # 绘制重要性图
        sorted_params = sorted(correlations.items(), key=lambda x: x[1], reverse=True)
        names, importances = zip(*sorted_params)

        plt.figure(figsize=(12, 8))
        bars = plt.barh(names, importances)
        plt.title('参数重要性分析（基于与评分的相关性）')
        plt.xlabel('重要性（相关系数绝对值）')
        plt.ylabel('参数')

        # 添加数值标签
        for i, (name, importance) in enumerate(sorted_params):
            plt.text(importance + 0.01, i, f'{importance:.3f}', va='center')

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'parameter_importance.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_convergence_analysis(self):
        """绘制收敛性分析"""
        if not self.convergence_data:
            return

        iterations = [cd['iteration'] for cd in self.convergence_data]
        best_scores = [cd['best_score'] for cd in self.convergence_data]
        current_scores = [cd['current_score'] for cd in self.convergence_data]

        plt.figure(figsize=(12, 6))
        plt.plot(iterations, current_scores, 'b-', alpha=0.5, label='当前评分')
        plt.plot(iterations, best_scores, 'r-', linewidth=2, label='最佳评分')
        plt.title('贝叶斯优化收敛性分析')
        plt.xlabel('迭代次数')
        plt.ylabel('评分')
        plt.legend()
        plt.grid(True)

        # 添加收敛指标
        if len(best_scores) > 10:
            # 计算收敛速度（最后10%的改进）
            last_10_percent = int(len(best_scores) * 0.1)
            if last_10_percent > 0:
                recent_improvement = best_scores[-last_10_percent] - best_scores[-1]
                plt.text(0.02, 0.98, f'近期改进: {recent_improvement:.6f}',
                        transform=plt.gca().transAxes, va='top')

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'convergence_analysis.png'), dpi=300, bbox_inches='tight')
        plt.close()
